<?php
/**
 * Main loader class for the plugin
 */
class RID_COD_Loader {

    /**
     * Initialize the plugin
     */
    public function init() {
        // Register scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'register_scripts'));
        add_action('wp_enqueue_scripts', array($this, 'add_dynamic_styles'), 20); // Add dynamic styles after main styles

        // Initialize Shipping Manager
        // Run migration on first load
        RID_COD_Shipping_Manager::migrate_algeria_shipping_costs();

        // Initialize form display
        $form = new RID_COD_Form();

        // Initialize AJAX handler
        $ajax = new RID_COD_Ajax();

        // Initialize Customizer settings (which now handles Google Sheets settings too)
        $customizer = new RID_COD_Customizer();

        // Initialize Product Meta handler (for product-level form control)
        $product_meta = new RID_COD_Product_Meta();

        // Add shortcode
        add_shortcode('rid_cod_form', array($form, 'shortcode_callback'));

        // Hook into the thank you page only if custom summary is not disabled
        $disable_custom_thankyou = get_option('rid_cod_disable_custom_thankyou', 'no') === 'yes';
        if (!$disable_custom_thankyou) {
            add_action('woocommerce_thankyou', array($this, 'display_custom_order_summary'), 5, 1); // Use priority 5 to run before default details
        }
    }

    /**
     * Register and enqueue scripts and styles
     */
    public function register_scripts() {
        // Enqueue on product pages, pages with the shortcode, or the order received page
        if (is_product() || (is_a(get_post(), 'WP_Post') && has_shortcode(get_post()->post_content, 'rid_cod_form')) || is_order_received_page()) {
            // Register styles
            wp_enqueue_style(
                'rid-cod-style',
                RID_COD_PLUGIN_URL . 'assets/css/rid-cod.css',
                array(),
                RID_COD_VERSION
            );

            // Register scripts
            wp_enqueue_script('jquery');

            // Ensure WooCommerce scripts are loaded
            if (function_exists('WC')) {
                // Force enqueue WooCommerce variation script with proper dependencies
                wp_enqueue_script('wp-util'); // Required for wp.template
                wp_enqueue_script('jquery-blockui'); // Required for blocking UI
                wp_enqueue_script('wc-add-to-cart-variation');

                // Load the variation template for wp.template
                if (function_exists('wc_get_template')) {
                    wc_get_template('single-product/add-to-cart/variation.php');
                }
            }

            wp_enqueue_script(
                'rid-cod-script',
                RID_COD_PLUGIN_URL . 'assets/js/rid-cod.js',
                array('jquery'),
                RID_COD_VERSION,
                true
            );

            // Enqueue WooCommerce variations script with proper dependencies
            $variation_deps = array('jquery', 'rid-cod-script');
            if (wp_script_is('wc-add-to-cart-variation', 'registered')) {
                $variation_deps[] = 'wc-add-to-cart-variation';
            }
            if (wp_script_is('wp-util', 'registered')) {
                $variation_deps[] = 'wp-util';
            }

            wp_enqueue_script(
                'rid-cod-variations',
                RID_COD_PLUGIN_URL . 'assets/js/rid-cod-variations.js',
                $variation_deps,
                RID_COD_VERSION,
                true
            );

            // Localize variation script with WooCommerce parameters
            if (function_exists('WC')) {
                wp_localize_script('rid-cod-variations', 'wc_add_to_cart_variation_params', array(
                    'wc_ajax_url' => WC_AJAX::get_endpoint('%%endpoint%%'),
                    'i18n_no_matching_variations_text' => esc_attr__('Sorry, no products matched your selection. Please choose a different combination.', 'woocommerce'),
                    'i18n_make_a_selection_text' => esc_attr__('Please select some product options before adding this product to your cart.', 'woocommerce'),
                    'i18n_unavailable_text' => esc_attr__('Sorry, this product is unavailable. Please choose a different combination.', 'woocommerce'),
                ));
            }

            // Enqueue Google Sheets debugging script if debug mode is enabled
            if (isset($_GET['rid_cod_debug']) && $_GET['rid_cod_debug'] === '1') {
                wp_enqueue_script(
                    'rid-cod-google-sheets-debug',
                    RID_COD_PLUGIN_URL . 'assets/js/rid-cod-google-sheets-debug.js',
                    array('jquery', 'rid-cod-script'),
                    RID_COD_VERSION,
                    true
                );
            }
        }
    }

    /**
     * Display custom order summary on the thank you page.
     * Removes default details and adds a custom structure.
     *
     * @param int $order_id The ID of the order.
     */
    public function display_custom_order_summary($order_id) {
        if (!$order_id) {
            return;
        }

        $order = wc_get_order($order_id);
        if (!$order) {
            return;
        }

        // Remove default WooCommerce order details
        remove_action('woocommerce_thankyou', 'woocommerce_order_details_table', 10);

        // Start custom output
        echo '<div class="rid-cod-thankyou-summary">';
        echo '<h2>' . esc_html__('ملخص الطلب الخاص بك', 'rid-cod') . '</h2>';

        // --- Customer Details ---
        echo '<div class="rid-section customer-details">';
        echo '<h3>' . esc_html__('معلومات الزبون', 'rid-cod') . '</h3>';
        echo '<p><strong>' . esc_html(get_option('rid_cod_field_name', __('الاسم الكامل', 'rid-cod'))) . ':</strong> ' . esc_html(trim($order->get_billing_first_name() . ' ' . $order->get_billing_last_name())) . '</p>';
        echo '<p><strong>' . esc_html(get_option('rid_cod_field_phone', __('رقم الهاتف', 'rid-cod'))) . ':</strong> ' . esc_html($order->get_billing_phone()) . '</p>';
        echo '<p><strong>' . esc_html(get_option('rid_cod_field_state', __('الولاية', 'rid-cod'))) . ':</strong> ' . esc_html($order->get_billing_state()) . '</p>'; // Consider getting full state name if code is stored
        echo '<p><strong>' . esc_html(get_option('rid_cod_field_city', __('البلدية', 'rid-cod'))) . ':</strong> ' . esc_html($order->get_billing_city()) . '</p>';
        if ($order->get_billing_address_1()) {
            echo '<p><strong>' . esc_html__('العنوان:', 'rid-cod') . '</strong> ' . esc_html($order->get_billing_address_1()) . '</p>';
        }
        
        // Display Delivery Type from order meta if available
        $delivery_type = get_post_meta($order->get_id(), '_rid_cod_delivery_type', true);
        if (!empty($delivery_type)) {
            $home_label = get_option('rid_cod_delivery_type_home_label', __('توصيل للمنزل', 'rid-cod'));
            $desk_label = get_option('rid_cod_delivery_type_desk_label', __('توصيل للمكتب', 'rid-cod'));
            $delivery_type_label = ($delivery_type === 'desk') ? $desk_label : $home_label;
            echo '<p><strong>' . esc_html__('طريقة التوصيل:', 'rid-cod') . '</strong> ' . esc_html($delivery_type_label) . '</p>';
        }
        echo '</div>'; // .customer-details

        // --- Order Items ---
        echo '<div class="rid-section order-items">';
        echo '<h3>' . esc_html__('المنتجات المطلوبة', 'rid-cod') . '</h3>';
        echo '<table class="rid-items-table">';
        echo '<thead><tr><th>' . esc_html__('المنتج', 'rid-cod') . '</th><th>' . esc_html(get_option('rid_cod_field_quantity', __('الكمية', 'rid-cod'))) . '</th><th>' . esc_html__('السعر', 'rid-cod') . '</th></tr></thead>';
        echo '<tbody>';
        foreach ($order->get_items() as $item_id => $item) {
            $product = $item->get_product();
            $product_name = $item->get_name();
            // Get and display formatted meta data (variations)
            $item_meta_data = $item->get_formatted_meta_data(''); // Pass empty string to get default format
            if ($item_meta_data) {
                $product_name .= '<div class="rid-variation-details">'; // Add a container for styling
                foreach ($item_meta_data as $meta_id => $meta) {
                    // Display key and value, stripping HTML from key for safety
                    $product_name .= '<span class="rid-variation-meta"><span class="rid-variation-key">' . wp_strip_all_tags($meta->display_key) . ':</span> ' . wp_kses_post($meta->display_value) . '</span>';
                }
                $product_name .= '</div>';
            }

            echo '<tr>';
            echo '<td>' . wp_kses_post($product_name) . '</td>';
            echo '<td>' . esc_html($item->get_quantity()) . '</td>';
            echo '<td>' . wp_kses_post($order->get_formatted_line_subtotal($item)) . '</td>';
            echo '</tr>';
        }
        echo '</tbody>';
        echo '</table>';
        echo '</div>'; // .order-items

        // --- Order Totals ---
        echo '<div class="rid-section order-totals">';
        echo '<h3>' . esc_html__('ملخص السعر', 'rid-cod') . '</h3>';
        echo '<table class="rid-totals-table">';
        // Subtotal (optional, usually same as total for single product COD)
        // echo '<tr><td>' . esc_html__('المجموع الفرعي:', 'rid-cod') . '</td><td>' . wp_kses_post($order->get_subtotal_to_display()) . '</td></tr>';
        foreach ($order->get_order_item_totals() as $key => $total) {
             // Skip payment method row if needed, or customize display
             if ($key === 'payment_method') continue;
             echo '<tr><td>' . wp_kses_post($total['label']) . '</td><td>' . wp_kses_post($total['value']) . '</td></tr>';
        }
        // Ensure Total is bold or emphasized
        // The loop above should handle the total row correctly based on WC settings.
        echo '</table>';
        echo '</div>'; // .order-totals

        echo '</div>'; // .rid-cod-thankyou-summary

        // Add some basic inline styles for now, or rely on the enqueued CSS
        // Note: These styles might be overridden by the dynamic styles added later.
        echo '<style>
            .rid-cod-thankyou-summary { border: 1px solid #ddd; padding: 20px; margin-top: 20px; background-color: #f9f9f9; border-radius: 5px; }
            .rid-cod-thankyou-summary h2 { text-align: center; margin-bottom: 25px; color: #333; }
            .rid-cod-thankyou-summary h3 { border-bottom: 1px solid #eee; padding-bottom: 8px; margin-bottom: 15px; color: #555; font-size: 1.1em; }
            .rid-section { margin-bottom: 20px; padding: 15px; background-color: #fff; border: 1px solid #eee; border-radius: 3px;}
            .rid-section p { margin: 0 0 8px; line-height: 1.6; }
            .rid-section p strong { display: inline-block; min-width: 80px; color: #444; }
            .rid-items-table, .rid-totals-table { width: 100%; border-collapse: collapse; }
            .rid-items-table th, .rid-items-table td, .rid-totals-table td { padding: 10px; border: 1px solid #eee; text-align: right; }
            .rid-items-table th { background-color: #f5f5f5; font-weight: bold; text-align: center;}
            .rid-items-table td:first-child, .rid-totals-table td:first-child { text-align: right; font-weight: bold; }
            .rid-totals-table tr:last-child td { font-weight: bold; font-size: 1.2em; color: #000; border-top: 2px solid #ddd;}
            .rid-items-table small { color: #777; font-size: 0.9em; }
        </style>';

    }

    /**
     * Add dynamic inline styles based on settings
     */
    public function add_dynamic_styles() {
        // Only add styles if the main style is already enqueued
        if (!wp_style_is('rid-cod-style', 'enqueued')) {
            return;
        }

        // Get saved colors with defaults
        $primary_bg_color = get_option('rid_cod_color_primary', '#ffffff'); // Updated default
        $button_bg_color = get_option('rid_cod_color_button_bg', '#6a3de8'); // Updated default
        $button_text_color = get_option('rid_cod_color_button_text', '#ffffff');
        $accent_color = get_option('rid_cod_color_accent', '#6a3de8');
        // Sticky Button Colors
        $sticky_button_bg_color = get_option('rid_cod_sticky_button_bg_color', '#6a3de8');
        $sticky_button_text_color = get_option('rid_cod_sticky_button_text_color', '#ffffff');
        $sticky_button_border_color = get_option('rid_cod_sticky_button_border_color', '#6a3de8');

        // Basic sanitization (should already be hex from settings save)
        $primary_bg_color = sanitize_hex_color($primary_bg_color) ?: '#ffffff';
        $button_bg_color = sanitize_hex_color($button_bg_color) ?: '#6a3de8';
        $button_text_color = sanitize_hex_color($button_text_color) ?: '#ffffff';
        $accent_color = sanitize_hex_color($accent_color) ?: '#6a3de8';
        // Sticky Button Colors Sanitize
        $sticky_button_bg_color = sanitize_hex_color($sticky_button_bg_color) ?: '#6a3de8';
        $sticky_button_text_color = sanitize_hex_color($sticky_button_text_color) ?: '#ffffff';
        $sticky_button_border_color = sanitize_hex_color($sticky_button_border_color) ?: '#6a3de8';

        // Convert accent color to RGB for rgba usage
        $accent_rgb = $this->hex_to_rgb($accent_color);

        // Build CSS string using CSS Variables
        $custom_css = "
            /* RID COD Dynamic Styles - Setting CSS Variables */
            :root {
                --rid-cod-primary-bg-color: {$primary_bg_color};
                --rid-cod-button-bg-color: {$button_bg_color};
                --rid-cod-button-text-color: {$button_text_color};
                --rid-cod-accent-color: {$accent_color};
                --rid-cod-accent-color-rgb: {$accent_rgb};
                /* Sticky Button Variables */
                --rid-cod-sticky-button-bg-color: {$sticky_button_bg_color};
                --rid-cod-sticky-button-text-color: {$sticky_button_text_color};
                --rid-cod-sticky-button-border-color: {$sticky_button_border_color};
                /* Add other variables like border-color, text-color if needed */
            }
        ";

        // Add inline style
        wp_add_inline_style('rid-cod-style', $custom_css);
    }

    /**
     * Convert hex color to RGB values
     */
    private function hex_to_rgb($hex) {
        // Remove # if present
        $hex = ltrim($hex, '#');

        // Convert to RGB
        if (strlen($hex) == 6) {
            $r = hexdec(substr($hex, 0, 2));
            $g = hexdec(substr($hex, 2, 2));
            $b = hexdec(substr($hex, 4, 2));
        } elseif (strlen($hex) == 3) {
            $r = hexdec(str_repeat(substr($hex, 0, 1), 2));
            $g = hexdec(str_repeat(substr($hex, 1, 1), 2));
            $b = hexdec(str_repeat(substr($hex, 2, 1), 2));
        } else {
            // Default to modern purple if invalid
            return '177, 156, 217';
        }

        return "$r, $g, $b";
    }
}